document.addEventListener('DOMContentLoaded', function() {
  initCardDetails();
  initDateTimePicker();
});

function initCardDetails() {
  const saveButton = document.getElementById('saveCardChanges');
  if (saveButton) {
    saveButton.addEventListener('click', function() {
      saveAllCardChanges();
    });
  }

  const takePaymentButton = document.querySelector('button[data-action="take-payment"]');
  if (takePaymentButton) {
    takePaymentButton.addEventListener('click', function() {
      handleTakePayment();
    });
  }



  initLastContactedButton();

  const descriptionTextarea = document.getElementById('card-description');
  if (descriptionTextarea) {
    descriptionTextarea.addEventListener('input', function() {
      this.style.height = 'auto';
      this.style.height = (this.scrollHeight) + 'px';
    });
  }

  initializeChecklistItems();

  const moveButton = document.querySelector('button[data-action="move-card"]');
  if (moveButton) {
    moveButton.addEventListener('click', function() {
      showMoveCardModal();
    });
  }

  const archiveButton = document.querySelector('button[data-action="archive-card"]');
  if (archiveButton) {
    archiveButton.addEventListener('click', function() {
      showArchiveCardModal();
    });
  }

  const teamButton = document.getElementById('teamDropdown');
  const teamDropdown = document.getElementById('teamDropdownMenu');
  if (teamButton && teamDropdown) {
    teamButton.addEventListener('click', function() {
      teamDropdown.classList.toggle('hidden');
    });

    document.addEventListener('click', function(event) {
      if (!teamButton.contains(event.target) && !teamDropdown.contains(event.target)) {
        teamDropdown.classList.add('hidden');
      }
    });
  }

  const checklistButton = document.getElementById('checklistDropdown');
  const checklistDropdown = document.getElementById('checklistDropdownMenu');
  if (checklistButton && checklistDropdown) {
    checklistButton.addEventListener('click', function() {
      checklistDropdown.classList.toggle('hidden');
    });

    document.addEventListener('click', function(event) {
      if (!checklistButton.contains(event.target) && !checklistDropdown.contains(event.target)) {
        checklistDropdown.classList.add('hidden');
      }
    });
  }

  // Labels dropdown functionality is handled by crm_labels.js
}

function initializeChecklistItems() {
  const checklistItems = document.querySelectorAll('.checklist-item');
  checklistItems.forEach(item => {
    const checkbox = item.querySelector('.checklist-checkbox');
    if (checkbox) {
      checkbox.addEventListener('change', function() {
        updateChecklistProgress();
      });
    }
  });

  const addItemButton = document.querySelector('button[data-action="add-checklist-item"]');
  const addItemInput = document.getElementById('add-checklist-item');
  if (addItemButton && addItemInput) {
    addItemButton.addEventListener('click', function() {
      addChecklistItem(addItemInput.value);
      addItemInput.value = '';
    });

    addItemInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        addItemButton.click();
      }
    });
  }

  updateChecklistProgress();
}

function updateChecklistProgress() {
  const totalItems = document.querySelectorAll('.checklist-item').length;
  const checkedItems = document.querySelectorAll('.checklist-checkbox:checked').length;
  
  if (totalItems > 0) {
    const progressPercent = Math.round((checkedItems / totalItems) * 100);
    const progressBar = document.querySelector('.checklist-progress-bar');
    const progressText = document.querySelector('.checklist-progress-text');
    const progressCount = document.querySelector('.checklist-progress-count');
    
    if (progressBar) progressBar.style.width = `${progressPercent}%`;
    if (progressText) progressText.textContent = `${progressPercent}%`;
    if (progressCount) progressCount.textContent = `${checkedItems}/${totalItems} completed`;
  }
}

function addChecklistItem(text) {
  if (!text.trim()) return;
  
  const checklistContainer = document.querySelector('.checklist-items');
  if (!checklistContainer) return;
  
  const newItem = document.createElement('div');
  newItem.className = 'flex items-center p-2 hover:bg-gray-50 rounded-lg transition-colors group checklist-item';
  newItem.innerHTML = `
    <div class="w-5 h-5 rounded-md border border-gray-300 mr-3 shadow-sm checklist-checkbox"></div>
    <span class="text-[14px] text-gray-800">${text}</span>
    <div class="ml-auto opacity-0 group-hover:opacity-100 transition-opacity">
      <button class="p-1 text-gray-400 hover:text-gray-600">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-pen w-3 h-3">
          <path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
          <path d="M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z"></path>
        </svg>
      </button>
    </div>
  `;
  
  checklistContainer.appendChild(newItem);
  updateChecklistProgress();
}

function saveAllCardChanges() {
  const saveButton = document.getElementById('saveCardChanges');
  if (!saveButton) return;

  const originalButtonText = saveButton.innerHTML;
  saveButton.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Saving...';
  saveButton.disabled = true;

  const cardId = window.location.pathname.split('/').pop();
  const description = document.getElementById('card-description').value;

  const customFieldValues = {};
  document.querySelectorAll('.custom-field-item').forEach(item => {
    const fieldId = item.getAttribute('data-field-id');
    const value = item.querySelector('.custom-field-value').textContent;
    customFieldValues[fieldId] = value;
  });

  const checklistItems = [];
  document.querySelectorAll('.checklist-item').forEach(item => {
    const text = item.querySelector('span').textContent;
    const isChecked = item.querySelector('.checklist-checkbox').checked;
    checklistItems.push({ text, completed: isChecked });
  });

  showNotification('Saving changes...', 'Saving all changes to the card...');


  fetch(`/admin/crm/cards/${cardId}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
      card: {
        description: description,
        custom_field_values: customFieldValues,
        checklist_items: checklistItems
      }
    })
  })
  .then(response => {
    if (!response.ok) {
      throw new Error('Failed to save changes');
    }
    return response.json();
  })
  .then(data => {
    showNotification('Success', 'All changes saved successfully!', 'success');

    saveButton.innerHTML = originalButtonText;
    saveButton.disabled = false;
  })
  .catch(error => {
    showNotification('Error', error.message || 'Failed to save changes', 'error');

    saveButton.innerHTML = originalButtonText;
    saveButton.disabled = false;
  });
}

function showNotification(title, message, type = 'info') {
  window.modalUtils.showModal({
    title: title,
    content: message,
    icon: type === 'error' ? 'error' : (type === 'success' ? 'success' : 'info'),
    showConfirmButton: true,
    confirmButtonText: 'OK',
    confirmButtonColor: 'bg-sky-300 hover:bg-sky-400'
  });
}

function handleTakePayment() {
  window.modalUtils.showModal({
    title: 'Take Payment',
    content: `
      <div class="space-y-4">
        <div>
          <label class="text-[14px] font-medium text-gray-500 block mb-2">Payment Amount</label>
          <input type="number" id="payment-amount" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm" placeholder="0.00">
        </div>
        <div>
          <label class="text-[14px] font-medium text-gray-500 block mb-2">Payment Method</label>
          <select id="payment-method" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
            <option value="credit_card">Credit Card</option>
            <option value="debit_card">Debit Card</option>
            <option value="cash">Cash</option>
            <option value="bank_transfer">Bank Transfer</option>
          </select>
        </div>
        <div>
          <label class="text-[14px] font-medium text-gray-500 block mb-2">Notes</label>
          <textarea id="payment-notes" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm" rows="3"></textarea>
        </div>
      </div>
    `,
    showCancelButton: true,
    confirmButtonText: 'Process Payment',
    cancelButtonText: 'Cancel',
    confirmButtonColor: 'bg-green-500 hover:bg-green-600',
    cancelButtonColor: 'border-gray-100 text-gray-500 hover:bg-gray-50/70 hover:text-gray-600',
    html: true,
    preConfirm: () => {
      const amount = document.getElementById('payment-amount').value;
      const method = document.getElementById('payment-method').value;
      const notes = document.getElementById('payment-notes').value;

      if (!amount || parseFloat(amount) <= 0) {
        modalUtils.showValidationMessage('Please enter a valid payment amount');
        return false;
      }

      return {
        amount,
        method,
        notes
      };
    }
  }).then((result) => {
    if (result.isConfirmed) {
      showNotification('Success', 'Payment processed successfully!', 'success');
    }
  });
}



function showMoveCardModal() {
  window.modalUtils.showModal({
    title: 'Move Card',
    content: `
      <div class="space-y-4">
        <div>
          <label class="text-[14px] font-medium text-gray-500 block mb-2">Select Board</label>
          <select id="select-board" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
            ${generateBoardOptions()}
          </select>
        </div>
        <div>
          <label class="text-[14px] font-medium text-gray-500 block mb-2">Select List</label>
          <select id="select-list" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
            ${generateListOptions()}
          </select>
        </div>
      </div>
    `,
    showCancelButton: true,
    confirmButtonText: 'Move Card',
    cancelButtonText: 'Cancel',
    confirmButtonColor: 'bg-sky-300 hover:bg-sky-400',
    cancelButtonColor: 'border-gray-100 text-gray-500 hover:bg-gray-50/70 hover:text-gray-600',
    html: true,
    preConfirm: () => {
      const boardId = document.getElementById('select-board').value;
      const listId = document.getElementById('select-list').value;

      if (!boardId) {
        modalUtils.showValidationMessage('Please select a board');
        return false;
      }

      if (!listId) {
        modalUtils.showValidationMessage('Please select a list');
        return false;
      }

      return {
        boardId,
        listId
      };
    }
  }).then((result) => {
    if (result.isConfirmed) {
      const cardId = window.location.pathname.split('/').pop();
      const targetBoardId = result.value.boardId;
      const targetListId = result.value.listId;

      // Get current board and list IDs to check if card is actually being moved
      const currentBoardId = document.querySelector('meta[name="current-board-id"]')?.content ||
                            document.querySelector('[data-card-board-id]')?.dataset.cardBoardId ||
                            document.getElementById('board-id')?.value;
      const currentListId = document.querySelector('[data-card-list-id]')?.dataset.cardListId ||
                           document.getElementById('list-id')?.value;

      const isActualMove = (targetBoardId !== currentBoardId) || (targetListId !== currentListId);

      fetch(`/admin/crm/cards/${cardId}/move`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
          list_id: targetListId
        })
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Failed to move card');
        }
        return response.json();
      })
      .then(data => {
        // Only show success notification if card was actually moved to a different location
        if (isActualMove) {
          showNotification('Success', 'Card moved successfully!', 'success');
        }
        window.location.href = `/admin/crm/boards/${targetBoardId}`;
      })
      .catch(error => {

        showNotification('Error', error.message || 'Failed to move card', 'error');
      });
    }
  });
}

function showArchiveCardModal() {
  window.modalUtils.showModal({
    title: 'Archive Card',
    content: 'Are you sure you want to archive this card? This action cannot be undone.',
    showCancelButton: true,
    confirmButtonText: 'Archive',
    cancelButtonText: 'Cancel',
    confirmButtonColor: 'bg-red-500 hover:bg-red-600',
    cancelButtonColor: 'border-gray-100 text-gray-500 hover:bg-gray-50/70 hover:text-gray-600'
  }).then((result) => {
    if (result.isConfirmed) {
      const cardId = window.location.pathname.split('/').pop();
      
      fetch(`/admin/crm/cards/${cardId}`, {
        method: 'DELETE',
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Failed to archive card');
        }
        return response.json();
      })
      .then(data => {
        window.location.href = `/admin/crm/boards/${data.board_id}`;
      })
      .catch(error => {

        showNotification('Error', error.message || 'Failed to archive card', 'error');
      });
    }
  });
}

function generateBoardOptions() {
  const currentBoardId = document.querySelector('meta[name="current-board-id"]')?.content;
  const currentBoardName = document.querySelector('meta[name="current-board-name"]')?.content || 'Current Board';
  
  return `<option value="${currentBoardId}" selected>${currentBoardName}</option>`;
}

function generateListOptions(boardId) {
  const lists = window.availableLists.filter(list => list.board_id === parseInt(boardId));
  return lists.map(list => `<option value="${list.id}">${list.name}</option>`).join('');
}

function initDateTimePicker() {
}

function initLastContactedButton() {
}

function updateLastContactedTime(datetimeValue) {
  if (!datetimeValue) return;
  
  const cardId = document.querySelector('[data-card-id]')?.dataset.cardId;
  if (!cardId) {
    if (typeof toastr !== 'undefined') {
      toastr.error('Card ID not found');
    } else {

    }
    return;
  }
  
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
  
  fetch(`/admin/crm/cards/${cardId}/update_last_contacted`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': csrfToken
    },
    body: JSON.stringify({
      last_contacted_at: datetimeValue
    })
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    if (data.success) {
      if (typeof toastr !== 'undefined') {
        toastr.success('Last contacted time updated');
      }

      // Refresh the page to show updated time
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  })
  .catch(error => {

    if (typeof toastr !== 'undefined') {
      toastr.error('Failed to update last contacted time');
    }
  });
}

function setQuickTime(preset) {
const datetimeInput = document.getElementById('lastContactedDateTime');
if (!datetimeInput) return;
  
const now = new Date();
let targetDate = new Date(now);
  
switch (preset) {
  case 'now':
    break;
  case 'today':
    targetDate.setHours(9, 0, 0, 0);
    break;
  case 'yesterday':
    targetDate.setDate(targetDate.getDate() - 1);
    targetDate.setHours(9, 0, 0, 0);
    break;
  case 'lastWeek':
    targetDate.setDate(targetDate.getDate() - 7);
    targetDate.setHours(9, 0, 0, 0);
    break;
  default:
    return;
}
  
const year = targetDate.getFullYear();
const month = String(targetDate.getMonth() + 1).padStart(2, '0');
const day = String(targetDate.getDate()).padStart(2, '0');
const hours = String(targetDate.getHours()).padStart(2, '0');
const minutes = String(targetDate.getMinutes()).padStart(2, '0');
  
const formattedDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;
datetimeInput.value = formattedDateTime;
  
return formattedDateTime;
}

function applyDateTime() {
  const datetimeInput = document.getElementById('lastContactedDateTime');
  if (!datetimeInput || !datetimeInput.value) {
    showNotification('Error', 'Please select a valid date and time', 'error');
    return;
  }
  const cardId = document.querySelector('[data-card-id]')?.dataset.cardId;
  if (!cardId) {
    showNotification('Error', 'Card ID not found', 'error');
    return;
  }
  
  const applyButton = document.getElementById('applyDateTime');
  const originalText = applyButton.innerHTML;
  applyButton.innerHTML = '<div class="inline-block animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div> Updating...';
  applyButton.disabled = true;
  
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
  
  fetch(`/admin/crm/cards/${cardId}/update_last_contacted`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': csrfToken
    },
    body: JSON.stringify({
      last_contacted_at: datetimeInput.value
    })
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    const lastContactedSpan = document.querySelector('#lastContactedButton span');
    if (lastContactedSpan) {
      lastContactedSpan.textContent = data.time_ago + ' ago';
    }
    
    showNotification('Success', 'Last contacted time updated successfully', 'success');
    closeDatePicker();
  })
  .catch(error => {

    showNotification('Error', 'Failed to update last contacted time', 'error');
  })
  .finally(() => {
    applyButton.innerHTML = originalText;
    applyButton.disabled = false;
  });
}
