# frozen_string_literal: true

module Admin
  module Crm
    class CardsController < Admin::ApplicationController
      before_action :set_list, except: %i[
        move move_to_board edit show labels update_last_contacted update_description
        update_status index completed archived add_checklist remove_checklist
        archive
      ]
      before_action :set_card, only: %i[
        update destroy edit show labels update_last_contacted update_description
        update_status restore add_checklist remove_checklist archive
      ]

      def create
        Rails.logger.info("CardsController#create called with params: #{params.inspect}")
        Rails.logger.info("Card params: #{card_params.inspect}")

        Rails.logger.info("Request headers: #{request.headers.env.select { |k, _v| k.start_with?('HTTP_') }.inspect}")
        Rails.logger.info("Request method: #{request.method}")
        Rails.logger.info("Request format: #{request.format}")

        @card = ::Crm::CardService.create_card(@list.id, card_params)

        respond_to do |format|
          if @card.persisted?
            format.html { redirect_to admin_crm_board_path(@list.crm_board), notice: 'Card was successfully created.' }
            format.json { render json: successful_json_response }
          else
            format.html { redirect_to admin_crm_board_path(@list.crm_board), alert: @card.errors.full_messages.join(', ') }
            format.json { render json: { success: false, errors: @card.errors.full_messages } }
          end
        end
      end

      def index
        @board = CrmBoard.find(params[:board_id]) if params[:board_id].present?
        @list = CrmList.find(params[:list_id]) if params[:list_id].present?

        if params[:status].present?
          @cards = CrmCard.where(status: params[:status])

          render json: {
            success: true,
            cards: @cards.map do |card|
              {
                id: card.id,
                title: card.title,
                patient_name: card.patient&.full_name,
                completed_at: card.completed_at&.strftime('%b %d, %Y %I:%M %p')
              }
            end
          }
          return
        end

        @cards = if @list
                   @list.crm_cards.active.order(:position)
                 elsif @board
                   @board.crm_cards.active.includes(:crm_list).order(:position)
                 else
                   CrmCard.active.includes(:crm_list).order(:position)
                 end

        respond_to do |format|
          format.html
          format.json { render json: @cards }
        end
      end

      def update
        @card = ::Crm::CardService.update_card(@card.id, card_params)

        respond_to do |format|
          if @card.errors.empty?
            format.html { redirect_after_successful_update }
            format.json { render json: successful_json_response }
          else
            format.html { redirect_to admin_crm_board_path(@list.crm_board), alert: @card.errors.full_messages.join(', ') }
            format.json { render json: { success: false, errors: @card.errors.full_messages } }
          end
        end
      end

      def destroy
        ::Crm::CardService.delete_card(@card.id)

        respond_to do |format|
          format.html { redirect_to admin_crm_board_path(@list.crm_board), notice: 'Card was successfully deleted.' }
          format.json { render json: { success: true } }
        end
      end

      def move
        @card = CrmCard.find(params[:id])

        if params[:treatment_id].present?
          @card = ::Crm::CardService.update_treatment(@card.id, params[:treatment_id])

          respond_to do |format|
            if @card.errors.empty?
              format.html { redirect_to admin_crm_card_path(@card), notice: 'Treatment was successfully updated.' }
              format.json { render json: { success: true, card: @card } }
            else
              format.html { redirect_to admin_crm_card_path(@card), alert: @card.errors.full_messages.join(', ') }
              format.json { render json: { success: false, errors: @card.errors.full_messages } }
            end
          end
        elsif params[:cot_category_id].present?
          @card = ::Crm::CardService.update_cot_category(@card.id, params[:cot_category_id])

          respond_to do |format|
            if @card.errors.empty?
              format.html { redirect_to admin_crm_card_path(@card), notice: 'COT Category was successfully updated.' }
              format.json { render json: { success: true, card: @card } }
            else
              format.html { redirect_to admin_crm_card_path(@card), alert: @card.errors.full_messages.join(', ') }
              format.json { render json: { success: false, errors: @card.errors.full_messages } }
            end
          end
        else
          Rails.logger.info("Moving card #{params[:id]} to list #{params[:list_id]} at position #{params[:position]}")

          @card = ::Crm::CardService.move_card(
            params[:id],
            params[:list_id],
            params[:position]
          )

          respond_to do |format|
            if @card.errors.empty?
              format.html { redirect_to admin_crm_board_path(@card.crm_list.crm_board), notice: 'Card was successfully moved.' }
              format.json { render json: { success: true, card: @card } }
            else
              Rails.logger.error("Error moving card: #{@card.errors.full_messages.join(', ')}")
              format.html { redirect_to admin_crm_board_path(@card.crm_list.crm_board), alert: @card.errors.full_messages.join(', ') }
              format.json { render json: { success: false, errors: @card.errors.full_messages } }
            end
          end
        end
      rescue StandardError => e
        Rails.logger.error("Exception in move action: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))

        respond_to do |format|
          format.html { redirect_to admin_crm_boards_path, alert: "An error occurred: #{e.message}" }
          format.json { render json: { success: false, errors: ["An error occurred: #{e.message}"] }, status: :unprocessable_entity }
        end
      end

      def move_to_board
        @card = ::Crm::CardService.move_to_board(
          params[:id],
          params[:board_id],
          params[:list_id]
        )

        respond_to do |format|
          if @card.errors.empty?
            format.html { redirect_to admin_crm_board_path(params[:board_id]), notice: 'Card was successfully moved.' }
            format.json { render json: { success: true, card: @card } }
          else
            format.html { redirect_back(fallback_location: admin_crm_boards_path, alert: @card.errors.full_messages.join(', ')) }
            format.json { render json: { success: false, errors: @card.errors.full_messages } }
          end
        end
      end

      def edit
        respond_to do |format|
          format.html { render :edit }
          format.json { render json: card_with_audit_json_response }
        end
      rescue ActiveRecord::RecordNotFound
        respond_to do |format|
          format.html { redirect_to admin_crm_boards_path, alert: 'Card not found.' }
          format.json { render json: { success: false, error: 'Card not found' }, status: :not_found }
        end
      end

      def show
        @board = @card.crm_list.crm_board

        @cot_categories = CotCategory.where(practice_id: Current.practice_id)
                                     .select(:id, :name)
                                     .order(:name)

        @card_checklist_ids = @card.crm_checklists.pluck(:id)
        @all_checklists = CrmChecklist.joins(crm_board: :practice)
                                      .where(crm_boards: { practice_id: Current.practice_id })
                                      .select(:id, :title)

        @custom_fields = @board.crm_custom_fields.order(position: :asc)

        field_values = @card.crm_custom_field_values.to_a
        @custom_field_values = field_values.each_with_object({}) do |field_value, hash|
          hash[field_value.crm_custom_field_id] = field_value.value
        end

        @card_activities = @card.card_activities.includes(user: { image_attachment: :blob })
        @card_comments = @card.card_comments.includes(user: { image_attachment: :blob }).where(ancestry: nil)

        respond_to do |format|
          format.html
          format.json { render json: card_with_audit_json_response }
        end
      rescue ActiveRecord::RecordNotFound
        respond_to do |format|
          format.html { redirect_to admin_crm_boards_path, alert: 'Card not found.' }
          format.json { render json: { success: false, error: 'Card not found' }, status: :not_found }
        end
      end

      def labels
        @board = @card.crm_list.crm_board
        labels_data = @board.available_labels

        render json: {
          success: true,
          card_labels: @card.crm_labels,
          board_labels: labels_data[:label_objects]
        }
      end

      def board_lists
        @board = CrmBoard.find(params[:board_id])
        render json: {
          success: true,
          lists: @board.crm_lists.as_json(only: %i[id title])
        }
      rescue ActiveRecord::RecordNotFound
        render json: { success: false, error: 'Board not found' }, status: :not_found
      end

      def completed
        @board = CrmBoard.includes(:practice).find(params[:board_id])
        @cards = @board.completed_cards
                       .includes(:patient, :crm_labels, crm_list: :crm_board)
                       .order(completed_at: :desc)
        @status_type = 'complete'
        @title = 'Completed Cards'

        render :status_cards
      end

      def archived
        @board = CrmBoard.includes(:practice).find(params[:board_id])
        @cards = @board.archived_cards
                       .includes({ patient: :assigned_staff }, :crm_labels, :cot_category, { crm_list: :crm_board })
                       .order(completed_at: :desc)
        @status_type = 'archive'
        @title = 'Archived Cards'

        render :status_cards
      end

      def update_last_contacted
        datetime = if params[:last_contacted_at].present?
                     Time.zone.parse(params[:last_contacted_at])
                   elsif params[:date].present?
                     Time.zone.parse(params[:date])
                   else
                     Time.current
                   end

        if @card.update(last_contacted_at: datetime)
          CardActivity.log_activity(
            card: @card,
            user: Current.user,
            activity_type: 'updated',
            description: "Updated last contacted date to #{@card.formatted_last_contacted}"
          )

          respond_to do |format|
            format.html { redirect_back(fallback_location: admin_crm_card_path(@card), notice: 'Last contacted date updated.') }
            format.json do
              time_ago = view_context.precise_time_ago_in_words(@card.last_contacted_at, include_seconds: true)
              render json: {
                success: true,
                last_contacted_at: @card.last_contacted_at,
                formatted_date: @card.formatted_last_contacted,
                time_ago: time_ago
              }
            end
          end
        else
          respond_to do |format|
            format.html { redirect_back(fallback_location: admin_crm_card_path(@card), alert: 'Failed to update last contacted date.') }
            format.json { render json: { success: false, errors: @card.errors.full_messages }, status: :unprocessable_entity }
          end
        end
      end

      def update_description
        if @card.update(description: params[:description])
          CardActivity.log_activity(
            card: @card,
            user: Current.user,
            activity_type: 'updated',
            description: 'Updated card description'
          )

          respond_to do |format|
            format.html { redirect_back(fallback_location: admin_crm_card_path(@card), notice: 'Description updated.') }
            format.json { render json: { success: true } }
          end
        else
          respond_to do |format|
            format.html { redirect_back(fallback_location: admin_crm_card_path(@card), alert: 'Failed to update description.') }
            format.json { render json: { success: false, errors: @card.errors.full_messages }, status: :unprocessable_entity }
          end
        end
      end

      def update_status
        status = params[:status]

        Rails.logger.info("Updating card status: #{@card.id} to #{status}")

        board = @card.crm_list.crm_board
        custom_statuses = board.crm_completion_zones.pluck(:name).map(&:downcase)
        valid_statuses = %w[complete archive] + custom_statuses

        if valid_statuses.include?(status)
          if @card.update(status: status, completed_at: Time.current)
            Rails.logger.info('Card status updated successfully')

            CardActivity.log_activity(
              card: @card,
              user: Current.user,
              activity_type: 'updated',
              description: "Card marked as #{status}"
            )

            render json: { success: true, card: @card.as_json }
          else
            Rails.logger.error("Failed to update card status: #{@card.errors.full_messages}")
            render json: { success: false, errors: @card.errors.full_messages }, status: :unprocessable_entity
          end
        else
          Rails.logger.error("Invalid status: #{status}")
          render json: { success: false, errors: ["Invalid status: #{status}"] }, status: :unprocessable_entity
        end
      end

      def restore
        @card = CrmCard.find(params[:id])

        if @card.update(status: 'active')
          CardActivity.log_activity(
            card: @card,
            user: Current.user,
            activity_type: 'updated',
            description: "Card restored from #{@card.status == 'complete' ? 'completed' : 'archived'} status"
          )

          render json: { success: true, card: @card.as_json }
        else
          render json: { success: false, errors: @card.errors.full_messages }, status: :unprocessable_entity
        end
      rescue StandardError => e
        Rails.logger.error("Exception in restore action: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        render json: { success: false, errors: ["Error restoring card: #{e.message}"] }, status: :internal_server_error
      end

      def archive
        Rails.logger.info("Archiving card: #{@card.id}")

        if @card.update(status: 'archive', completed_at: Time.current)
          CardActivity.log_activity(
            card: @card,
            user: Current.user,
            activity_type: 'archived',
            description: 'Card archived'
          )

          render json: { success: true, card: @card.as_json }
        else
          Rails.logger.error("Failed to archive card: #{@card.errors.full_messages}")
          render json: { success: false, errors: @card.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def add_checklist
        begin
          Rails.logger.info("Add checklist params: #{params.inspect}")

          @card = CrmCard.find(params[:id])
          Rails.logger.info("Found card: #{@card.inspect}")

          raw_checklist_id = params[:checklist_id]
          checklist_id_clean = raw_checklist_id.to_s.gsub(/"/, '')
          checklist_id = checklist_id_clean.to_i
          Rails.logger.info("Raw checklist_id: #{raw_checklist_id}, Cleaned: #{checklist_id_clean}, Final ID: #{checklist_id}")

          checklist = CrmChecklist.find_by(id: checklist_id)

          if checklist.nil?
            Rails.logger.error("Checklist not found with ID: #{checklist_id}")
            render json: { success: false, error: "Checklist not found with ID: #{checklist_id}" }, status: :not_found
            return
          end

          Rails.logger.info("Found checklist: #{checklist.inspect}")

          Rails.logger.info('Checking if checklist is already associated with card...')
          Rails.logger.info("Card checklists: #{@card.crm_checklists.pluck(:id)}")

          has_many_association = @card.crm_checklists.include?(checklist)
          Rails.logger.info("Has many association check: #{has_many_association}")

          join_table_query = "SELECT * FROM crm_cards_checklists WHERE crm_card_id = #{@card.id} AND crm_checklist_id = #{checklist.id}"
          Rails.logger.info("Join table query: #{join_table_query}")

          join_table_results = ActiveRecord::Base.connection.execute(join_table_query).to_a
          Rails.logger.info("Join table results: #{join_table_results.inspect}")

          join_exists = join_table_results.any?
          Rails.logger.info("Join table entry exists: #{join_exists}")

          old_relationship = checklist.crm_card_id == @card.id
          Rails.logger.info("Old relationship check: #{old_relationship}, checklist.crm_card_id: #{checklist.crm_card_id}")

          if has_many_association || join_exists
            Rails.logger.info('Checklist already added to card through has_and_belongs_to_many')
            Rails.logger.info("Association details: has_many_association=#{has_many_association}, join_exists=#{join_exists}")
            render json: { success: false, error: 'Checklist is already added to this card' }, status: :unprocessable_entity
            return
          end

          if checklist.crm_card_id == @card.id
            Rails.logger.info('Checklist already added to card through old has_many relationship')
            Rails.logger.info('Migrating checklist from old to new relationship')
            checklist.crm_card_id = nil
            checklist.save
          end

          begin
            Rails.logger.info("Before association - Card checklists: #{@card.crm_checklists.pluck(:id)}")
            query = "SELECT * FROM crm_cards_checklists WHERE crm_card_id = #{@card.id}"
            result = ActiveRecord::Base.connection.execute(query).to_a
            Rails.logger.info("Before association - Join table entries for card: #{result}")

            final_check_query = "SELECT * FROM crm_cards_checklists WHERE crm_card_id = #{@card.id} AND crm_checklist_id = #{checklist.id}"
            final_check_results = ActiveRecord::Base.connection.execute(final_check_query).to_a

            if final_check_results.any?
              Rails.logger.info('Final check shows association already exists!')
              Rails.logger.info("Final check results: #{final_check_results.inspect}")

              @card.reload # Make sure the card has the latest associations

              CardActivity.log_activity(
                card: @card,
                user: Current.user,
                activity_type: 'updated',
                description: "Added checklist: #{checklist.title}"
              )

              render json: { success: true, message: 'Checklist was already associated with this card' }
              return
            end

            insert_sql = "INSERT INTO crm_cards_checklists (crm_card_id, crm_checklist_id) VALUES (#{@card.id}, #{checklist.id})"
            Rails.logger.info("Executing SQL: #{insert_sql}")

            begin
              ActiveRecord::Base.connection.execute(insert_sql)
              Rails.logger.info('SQL insert successful')
            rescue ActiveRecord::StatementInvalid => e
              if e.message.include?('PG::UniqueViolation') || e.message.include?('already exists')
                Rails.logger.info('Unique constraint violation - association already exists')

                @card.reload # Make sure the card has the latest associations

                CardActivity.log_activity(
                  card: @card,
                  user: Current.user,
                  activity_type: 'updated',
                  description: "Added checklist: #{checklist.title}"
                )

                render json: { success: true, message: 'Checklist was already associated with this card' }
                return
              else
                Rails.logger.error("SQL insert failed: #{e.message}")
                begin
                  @card.crm_checklists << checklist
                  Rails.logger.info('Associated checklist with card using association method')
                rescue StandardError => assoc_error
                  Rails.logger.error("Association method also failed: #{assoc_error.message}")
                  raise assoc_error # Re-raise to be caught by the outer begin/rescue
                end
              end
            end

            @card.reload
            join_table_query = "SELECT * FROM crm_cards_checklists WHERE crm_card_id = #{@card.id} AND crm_checklist_id = #{checklist.id}"
            join_table_results = ActiveRecord::Base.connection.execute(join_table_query).to_a
            join_exists_after = join_table_results.any?

            Rails.logger.info("After association - Join table query: #{join_table_query}")
            Rails.logger.info("After association - Join table results: #{join_table_results.inspect}")
            Rails.logger.info("After association - Join table entry exists: #{join_exists_after}")
            Rails.logger.info("After association - Card checklists: #{@card.crm_checklists.pluck(:id)}")

            has_many_after = @card.crm_checklists.include?(checklist)
            Rails.logger.info("After association - Has many association check: #{has_many_after}")
          rescue StandardError => e
            Rails.logger.error("Error associating checklist with card: #{e.message}")
            Rails.logger.error(e.backtrace.join("\n"))
            raise e
          end

          CardActivity.log_activity(
            card: @card,
            user: Current.user,
            activity_type: 'updated',
            description: "Added checklist: #{checklist.title}"
          )

          render json: { success: true, message: 'Checklist added successfully' }
        rescue ActiveRecord::RecordNotFound => e
          Rails.logger.error("RecordNotFound error: #{e.message}")
          render json: { success: false, error: "Error adding checklist: #{e.message}" }, status: :not_found
        rescue StandardError => e
          Rails.logger.error("General error in add_checklist: #{e.message}")
          Rails.logger.error(e.backtrace.join("\n"))
          render json: { success: false, error: "Error adding checklist: #{e.message}" }, status: :unprocessable_entity
        end
      rescue StandardError => e
        Rails.logger.error("Exception in add_checklist action: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        render json: { success: false, error: "Error adding checklist: #{e.message}" }, status: :internal_server_error
      end

      def remove_checklist
        @card = CrmCard.find(params[:id])
        checklist = CrmChecklist.find(params[:checklist_id])

        unless @card.crm_checklists.include?(checklist)
          render json: { success: false, error: 'Checklist is not associated with this card' }, status: :unprocessable_entity
          return
        end

        @card.crm_checklists.delete(checklist)

        CardActivity.log_activity(
          card: @card,
          user: Current.user,
          activity_type: 'updated',
          description: "Removed checklist: #{checklist.title}"
        )

        render json: { success: true, message: 'Checklist removed successfully' }
      rescue StandardError => e
        Rails.logger.error("Exception in remove_checklist action: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        render json: { success: false, error: "Error removing checklist: #{e.message}" }, status: :internal_server_error
      end

      private

      def set_list
        @list = CrmList.find(params[:list_id])
      end

      def set_card
        @card = if @list
                  @list.crm_cards.includes(
                    :patient,
                    { crm_list: :crm_board },
                    :crm_labels,
                    :crm_custom_field_values
                  ).find(params[:id])
                else
                  CrmCard.includes(
                    :patient,
                    { crm_list: :crm_board },
                    :crm_labels,
                    :crm_custom_field_values
                  ).find(params[:id])
                end
      end

      def card_params
        params.require(:crm_card).permit(
          :title,
          :description,
          :position,
          :patient_id,
          :practice_id,
          :treatment_id,
          :cot_category_id,
          :last_contacted_at,
          labels: [],
          crm_custom_field_values_attributes: %i[id crm_custom_field_id value]
        )
      end

      def redirect_after_successful_update
        if request.referer&.include?(admin_crm_card_path(@card))
          redirect_to admin_crm_card_path(@card), notice: 'Card was successfully updated.'
        else
          redirect_to admin_crm_board_path(@list&.crm_board || @card.crm_list.crm_board), notice: 'Card was successfully updated.'
        end
      end

      def successful_json_response
        card_json = prepare_card_json

        {
          success: true,
          html: render_to_string(partial: 'admin/crm/cards/card', formats: [:html], locals: { card: @card }),
          card_id: @card.id,
          card: card_json
        }
      end

      def card_with_audit_json_response
        card_json = prepare_card_json

        card_json['audit'] = @card.audit if @card.respond_to?(:audit)

        {
          success: true,
          card: card_json
        }
      end

      def prepare_card_json
        card_json = @card.as_json
        if @card.patient.present?
          card_json['patient'] = {
            id: @card.patient.id,
            first_name: @card.patient.first_name,
            last_name: @card.patient.last_name,
            email: @card.patient.email,
            mobile_phone: @card.patient.mobile_phone,
            full_name: @card.patient.full_name
          }
        end
        card_json
      end
    end
  end
end
