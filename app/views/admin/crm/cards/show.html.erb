<% content_for :head do %>
  <meta name="current-board-id" content="<%= @board.id %>">
  <meta name="current-board-name" content="<%= @board.name %>">
  <meta name="current-list-id" content="<%= @card.crm_list_id %>">
<% end %>

<div class="min-h-screen bg-[#f5f5f7] text-[#1d1d1f] font-sf-pro" data-card-board-id="<%= @board.id %>" data-card-list-id="<%= @card.crm_list_id %>">

  <!-- Hidden fields for JavaScript -->
  <input type="hidden" id="card-id" value="<%= @card.id %>">
  <input type="hidden" id="board-id" value="<%= @board.id %>">
  <input type="hidden" id="list-id" value="<%= @card.crm_list_id %>">

  <%= render 'header', card: @card, board: @board %>

  <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
      <div class="lg:col-span-3">
        <div class="bg-amber-100 rounded-xl shadow-sm overflow-hidden mb-4">
          <div class="p-4 flex items-center justify-between">
            <%= render partial: 'admin/crm/shared/patient_info', locals: { patient: @card.patient } %>
          </div>
        </div>
        <div class="bg-white rounded-xl shadow-sm mb-4 p-3">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-4">
            <div>
              <h3 class="text-[14px] font-medium text-gray-800 mb-2">Assigned Team</h3>
              <% if @card.patient&.assigned_staff&.any? %>
                <div class="flex space-x-1.5">
                  <% @card.patient.assigned_staff.each do |user| %>
                    <div class="relative">
                      <div class="w-10 h-10 rounded-full overflow-hidden border border-gray-100 shadow-sm" data-tippy-content="<%= user.full_name %>">
                        <%= render 'layouts/shared/user_avatar', user: user, width: 40, height: 40 %>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="text-[12px] text-gray-500 italic">
                  No team members assigned
                </div>
              <% end %>
            </div>
            <div>
              <h3 class="text-[14px] font-medium text-gray-800 mb-2">Last Contacted</h3>
              <div class="relative">
                <div class="relative">
                  <div class="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-3.5 h-3.5 text-gray-600">
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                  </div>
                  <input 
                    type="datetime-local" 
                    id="lastContactedDateTime" 
                    class="w-full pl-9 pr-3 py-1.5 rounded-lg bg-gray-50 border border-gray-200 text-[12px] shadow-sm hover:bg-gray-100 transition-colors cursor-pointer focus:ring-1 focus:ring-gray-300 focus:outline-none" 
                    value="<%= @card.last_contacted_at&.strftime('%Y-%m-%dT%H:%M') %>"
                    max="<%= Time.current.strftime('%Y-%m-%dT%H:%M') %>"
                    onchange="updateLastContactedTime(this.value)"
                  >
                </div>
                
                <!-- No quick presets as requested -->
                
                <script>
                  // Define the updateLastContactedTime function inline
                  function updateLastContactedTime(datetimeValue) {
                    if (!datetimeValue) return;
                    
                    // Validate that the selected date is not in the future
                    const selectedDate = new Date(datetimeValue);
                    const currentDate = new Date();
                    
                    if (selectedDate > currentDate) {
                      if (typeof toastr !== 'undefined') {
                        toastr.error('Cannot set last contacted time to a future date');
                      } else {
                        console.error('Cannot set last contacted time to a future date');
                      }
                      
                      // Reset to current value
                      document.getElementById('lastContactedDateTime').value = 
                        '<%= @card.last_contacted_at&.strftime('%Y-%m-%dT%H:%M') %>';
                      return;
                    }
                    
                    const cardId = <%= @card.id %>;
                    if (!cardId) {
                      if (typeof toastr !== 'undefined') {
                        toastr.error('Card ID not found');
                      } else {
                        console.error('Card ID not found');
                      }
                      return;
                    }
                    
                    // Get CSRF token
                    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
                    
                    // Send AJAX request to update last_contacted_at
                    fetch(`/admin/crm/cards/${cardId}/update_last_contacted`, {
                      method: 'PATCH',
                      headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-Token': csrfToken
                      },
                      body: JSON.stringify({
                        last_contacted_at: datetimeValue
                      })
                    })
                    .then(response => {
                      if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                      }
                      return response.json();
                    })
                    .then(data => {
                      if (data.success) {
                        if (typeof toastr !== 'undefined') {
                          toastr.success('Last contacted time updated');
                        }
                        // Refresh the page to show updated time
                        setTimeout(() => {
                          window.location.reload();
                        }, 1000);
                      }
                    })
                    .catch(error => {
                      console.error('Error updating last contacted time:', error);
                      if (typeof toastr !== 'undefined') {
                        toastr.error('Failed to update last contacted time');
                      }
                    });
                  }
                </script>
              </div>
            </div>
            <div>
              <h3 class="text-[14px] font-medium text-gray-800 mb-2">COT Category</h3>
              <div class="relative">
                <% if @cot_categories.present? %>
                  <div class="cot-category-dropdown relative">
                    <button class="w-full flex items-center justify-between px-3 py-2 rounded-lg bg-white border border-gray-200 text-[12px] shadow-sm hover:bg-gray-50 transition-colors" type="button" id="cotCategoryDropdown" onclick="toggleCotCategoryDropdown()">
                      <span><%= @card.cot_category.present? ? @card.cot_category.name : 'Select COT Category' %></span>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down w-3.5 h-3.5 text-gray-400">
                        <polyline points="6 9 12 15 18 9"></polyline>
                      </svg>
                    </button>

                    <div id="cotCategoryDropdownMenu" class="hidden absolute left-0 right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                      <div class="max-h-48 overflow-y-auto py-1">
                        <% @cot_categories.each do |cot_category| %>
                          <div class="px-3 py-2 hover:bg-gray-50 cursor-pointer transition-colors cot-category-option <%= @card.cot_category&.id == cot_category.id ? 'bg-gray-100' : '' %>" data-cot-category-id="<%= cot_category.id %>" data-card-id="<%= @card.id %>">
                            <%= cot_category.name %>
                          </div>
                        <% end %>
                      </div>
                    </div>
                  </div>
                <% else %>
                  <button class="w-full flex items-center justify-between px-3 py-2 rounded-lg bg-white border border-gray-200 text-[12px] shadow-sm hover:bg-gray-50 transition-colors" disabled>
                    <span>No COT categories available</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down w-3.5 h-3.5 text-gray-400">
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  </button>
                <% end %>
              </div>
            </div>
          </div>
          
          <script>
            document.addEventListener('DOMContentLoaded', function() {
              // Close dropdowns when clicking outside
              document.addEventListener('click', function(e) {
                // COT Category dropdown
                const cotCategoryDropdown = document.getElementById('cotCategoryDropdownMenu');
                const cotCategoryButton = document.getElementById('cotCategoryDropdown');
                if (cotCategoryDropdown && !cotCategoryDropdown.contains(e.target) && !cotCategoryButton.contains(e.target)) {
                  cotCategoryDropdown.classList.add('hidden');
                }
                
                // Date picker dropdown
                const datePickerDropdown = document.getElementById('datePickerDropdown');
                const datePickerButton = document.getElementById('lastContactedButton');
                if (datePickerDropdown && !datePickerDropdown.contains(e.target) && !datePickerButton.contains(e.target)) {
                  datePickerDropdown.classList.add('hidden');
                }
                
                // Checklist dropdown
                const checklistDropdownMenu = document.getElementById('checklistDropdownMenu');
                const checklistDropdownButton = document.getElementById('checklistDropdown');
                if (checklistDropdownMenu && !checklistDropdownMenu.contains(e.target) && !checklistDropdownButton.contains(e.target)) {
                  checklistDropdownMenu.classList.add('hidden');
                }
              });
              
              // COT Category selection
              document.querySelectorAll('.cot-category-option').forEach(function(el) {
                el.addEventListener('click', function(e) {
                  e.preventDefault();
                  const cotCategoryId = this.getAttribute('data-cot-category-id');
                  const cardId = this.getAttribute('data-card-id');

                  // Update visual selection immediately
                  document.querySelectorAll('.cot-category-option').forEach(item => {
                    item.classList.remove('bg-gray-100');
                  });
                  this.classList.add('bg-gray-100');

                  // Update button text
                  const buttonText = this.textContent.trim();
                  document.querySelector('#cotCategoryDropdown span').textContent = buttonText;

                  // Hide dropdown
                  document.getElementById('cotCategoryDropdownMenu').classList.add('hidden');

                  // Send request to server
                  fetch(`/admin/crm/cards/${cardId}/move`, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                      'Accept': 'application/json',
                      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({ cot_category_id: cotCategoryId })
                  })
                  .then(async response => {
                    let data;
                    try {
                      data = await response.json();
                    } catch (parseError) {
                      console.error('Error parsing JSON response:', parseError);
                      alert('Unexpected server response.');
                      setTimeout(() => window.location.reload(), 2000);
                      return;
                    }
                    if (response.ok && data && data.success) {
                      toastr.success('Treatment updated successfully', 'Treatment Updated');
                      // Optionally show a success message (toastr, etc.)
                    } else {
                      const errorMsg = data && data.errors ? data.errors.join(', ') : 'Failed to update treatment';
                      alert('Error updating treatment: ' + errorMsg);
                      setTimeout(() => window.location.reload(), 2000);
                    }
                  })
                  .catch(error => {
                    console.error('Network error:', error);
                    alert('Network error occurred. Please try again.');
                    setTimeout(() => window.location.reload(), 2000);
                  });
                });
              });
              
              // Initialize the date picker
              initDatePicker();
              
              // Initialize checklist dropdown
              initChecklistDropdown();
              
              // Initialize checklist toggle functionality
              initChecklistToggle();
            });
            
            // Initialize checklist dropdown functionality
            function initChecklistDropdown() {
              const checklistDropdown = document.getElementById('checklistDropdown');
              const checklistDropdownMenu = document.getElementById('checklistDropdownMenu');
              
              if (checklistDropdown && checklistDropdownMenu) {
                // Toggle dropdown visibility
                checklistDropdown.addEventListener('click', function(e) {
                  e.preventDefault();
                  checklistDropdownMenu.classList.toggle('hidden');
                });
                
                // Handle checklist selection
                document.querySelectorAll('.checklist-option').forEach(option => {
                  option.addEventListener('click', function() {
                    const checklistId = this.getAttribute('data-checklist-id');
                    addChecklistToCard(checklistId);
                    checklistDropdownMenu.classList.add('hidden');
                  });
                });
                
                // Handle edit checklist button
                document.querySelectorAll('.edit-checklist').forEach(button => {
                  button.addEventListener('click', function(e) {
                    e.stopPropagation(); // Prevent triggering parent click event
                    const checklistId = this.getAttribute('data-checklist-id');
                    window.location.href = `/admin/crm/boards/<%= @board.id %>/checklists/${checklistId}/edit`;
                  });
                });
              }
            }
            

            
            // Team member functionality has been moved to the card_team_members.js module
            // We're using event delegation to handle add/remove operations
            
            function initChecklistToggle() {
              // Toggle checklist items
              // Handle checklist item toggle
              document.querySelectorAll('.toggle-checklist-item').forEach(item => {
                item.addEventListener('click', function() {
                  const itemId = this.getAttribute('data-item-id');
                  const checklistId = this.getAttribute('data-checklist-id');
                  
                  fetch(`/admin/crm/boards/<%= @board.id %>/checklists/${checklistId}/checklist_items/${itemId}/toggle`, {
                    method: 'PATCH',
                    headers: {
                      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
                      'Content-Type': 'application/json'
                    }
                  })
                  .then(response => response.json())
                  .then(data => {
                    if (data.success) {
                      // Update the UI
                      const itemElement = this.closest('.checklist-item');
                      const label = itemElement.querySelector('span');
                      
                      if (this.classList.contains('bg-gradient-to-br')) {
                        // Currently checked, so uncheck
                        this.classList.remove('bg-gradient-to-br', 'from-amber-50', 'to-amber-100', 'text-amber-700');
                        this.innerHTML = '';
                        label.classList.remove('line-through');
                      } else {
                        // Currently unchecked, so check
                        this.classList.add('bg-gradient-to-br', 'from-amber-50', 'to-amber-100', 'text-amber-700');
                        this.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check w-3 h-3"><path d="M20 6 9 17l-5-5"></path></svg>';
                        label.classList.add('line-through');
                      }
                      
                      // Update progress bar
                      const checklistContainer = this.closest('.checklist-container');
                      const progressBar = checklistContainer.querySelector('.bg-amber-400');
                      const completedCountEl = checklistContainer.querySelector('.text-xs.text-gray-500');
                      
                      if (progressBar && completedCountEl) {
                        progressBar.style.width = `${data.checklist.completion_percentage}%`;
                        completedCountEl.textContent = `${data.checklist.completed_items_count}/${data.checklist.items_count} items completed`;
                      }
                    }
                  })
                  .catch(error => console.error('Error:', error));
                });
              });
              
              // Handle delete checklist button
              document.querySelectorAll('.delete-checklist-btn').forEach(button => {
                button.addEventListener('click', function() {
                  if (confirm('Are you sure you want to remove this checklist from the card?')) {
                    const checklistId = this.getAttribute('data-checklist-id');
                    const cardId = this.getAttribute('data-card-id');
                    removeChecklistFromCard(checklistId);
                  }
                });
              });
            }
            
            // Add a checklist to a card
            function addChecklistToCard(checklistId) {
              const cardId = '<%= @card.id %>';
              
              fetch(`/admin/crm/cards/${cardId}/add_checklist`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                  'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                  checklist_id: checklistId
                })
              })
              .then(response => response.json())
              .then(data => {
                if (data.success) {
                  // Reload the page to show the added checklist
                  window.location.reload();
                } else {
                  alert('Error adding checklist: ' + (data.error || 'Unknown error'));
                }
              })
              .catch(error => {
                console.error('Error adding checklist:', error);
                alert('Error adding checklist. Please try again.');
              });
            }
            
            // Remove a checklist from a card
            function removeChecklistFromCard(checklistId) {
              const cardId = '<%= @card.id %>';
              
              fetch(`/admin/crm/cards/${cardId}/remove_checklist`, {
                method: 'DELETE',
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                  'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                  checklist_id: checklistId
                })
              })
              .then(response => response.json())
              .then(data => {
                if (data.success) {
                  // Remove the checklist from the UI
                  const checklistElement = document.querySelector(`.checklist-container[data-checklist-id="${checklistId}"]`);
                  if (checklistElement) {
                    checklistElement.remove();
                    
                    // Check if there are no more checklists
                    const checklistsContainer = document.getElementById('checklists-container');
                    if (!checklistsContainer.querySelector('.checklist-container')) {
                      checklistsContainer.innerHTML = `
                        <div class="text-center text-gray-500 py-8 bg-white rounded-lg shadow-sm border border-gray-200">
                          <p class="mb-3">No checklists added to this card yet.</p>
                          <p class="text-sm">Use the Checklists dropdown in the sidebar to add checklists.</p>
                        </div>
                      `;
                    }
                  }
                } else {
                  alert('Error removing checklist: ' + (data.error || 'Unknown error'));
                }
              })
              .catch(error => {
                console.error('Error removing checklist:', error);
                alert('Error removing checklist. Please try again.');
              });
            }
            
            // Add a new checklist item
            function addChecklistItem(content) {
              const cardId = '<%= @card.id %>';
              // Using multiple checklists per card now
              
              if (!checklistId) {
                alert('Please create a checklist first');
                return;
              }
              
              fetch(`/admin/crm/cards/${cardId}/checklist/checklist_items`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                  'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                  crm_checklist_item: {
                    content: content
                  }
                })
              })
              .then(response => response.json())
              .then(data => {
                if (data.id) {
                  // Add the new item to the UI
                  const checklistItems = document.getElementById('checklist-items');
                  
                  // Clear the 'no items' message if it exists
                  if (checklistItems.querySelector('.text-center')) {
                    checklistItems.innerHTML = '';
                  }
                  
                  const newItem = document.createElement('div');
                  newItem.className = 'flex items-center p-2 hover:bg-gray-50 rounded-lg transition-colors group checklist-item';
                  newItem.setAttribute('data-item-id', data.id);
                  
                  newItem.innerHTML = `
                    <div class="w-5 h-5 rounded-md border border-gray-300 flex items-center justify-center mr-3 shadow-sm cursor-pointer toggle-item"></div>
                    <div class="flex-1 text-[14px] text-gray-700">${content}</div>
                    <div class="opacity-0 group-hover:opacity-100 transition-opacity">
                      <button class="text-gray-400 hover:text-gray-600 transition-colors edit-item">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pencil w-3.5 h-3.5">
                          <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                          <path d="m15 5 4 4"></path>
                        </svg>
                      </button>
                      <button class="text-gray-400 hover:text-red-500 transition-colors ml-1 delete-item">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2 w-3.5 h-3.5">
                          <path d="M3 6h18"></path>
                          <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                          <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                          <line x1="10" x2="10" y1="11" y2="17"></line>
                          <line x1="14" x2="14" y1="11" y2="17"></line>
                        </svg>
                      </button>
                    </div>
                  `;
                  
                  checklistItems.appendChild(newItem);
                  
                  // Add event listeners to the new item
                  const toggleButton = newItem.querySelector('.toggle-item');
                  toggleButton.addEventListener('click', function() {
                    toggleChecklistItem(data.id, newItem);
                  });
                  
                  const editButton = newItem.querySelector('.edit-item');
                  editButton.addEventListener('click', function() {
                    const currentContent = newItem.querySelector('.flex-1').textContent.trim();
                    const newContent = prompt('Edit checklist item:', currentContent);
                    if (newContent && newContent !== currentContent) {
                      updateChecklistItem(data.id, newContent, newItem);
                    }
                  });
                  
                  const deleteButton = newItem.querySelector('.delete-item');
                  deleteButton.addEventListener('click', function() {
                    if (confirm('Are you sure you want to delete this item?')) {
                      deleteChecklistItem(data.id, newItem);
                    }
                  });
                  
                  // Update progress
                  updateChecklistProgress();
                }
              })
              .catch(error => {
                console.error('Error adding checklist item:', error);
                alert('Error adding checklist item. Please try again.');
              });
            }
            
            // Toggle a checklist item's completion status
            function toggleChecklistItem(itemId, itemElement) {
              const cardId = '<%= @card.id %>';
              // Using multiple checklists per card now
              const isCompleted = itemElement.querySelector('.toggle-item').classList.contains('bg-gradient-to-br');
              
              fetch(`/admin/crm/cards/${cardId}/checklist/checklist_items/${itemId}/toggle`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                  'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                }
              })
              .then(response => response.json())
              .then(data => {
                if (data.completed !== undefined) {
                  // Update the UI
                  const toggleButton = itemElement.querySelector('.toggle-item');
                  const textElement = itemElement.querySelector('.flex-1');
                  
                  if (data.completed) {
                    toggleButton.classList.add('bg-gradient-to-br', 'from-amber-50', 'to-amber-100', 'text-amber-700');
                    toggleButton.innerHTML = `
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check w-3 h-3">
                        <path d="M20 6 9 17l-5-5"></path>
                      </svg>
                    `;
                    textElement.classList.add('line-through');
                  } else {
                    toggleButton.classList.remove('bg-gradient-to-br', 'from-amber-50', 'to-amber-100', 'text-amber-700');
                    toggleButton.innerHTML = '';
                    textElement.classList.remove('line-through');
                  }
                  
                  // Update progress
                  updateChecklistProgress();
                }
              })
              .catch(error => {
                console.error('Error toggling checklist item:', error);
                alert('Error toggling checklist item. Please try again.');
              });
            }
            
            // Update a checklist item's content
            function updateChecklistItem(itemId, content, itemElement) {
              const cardId = '<%= @card.id %>';
              // Using multiple checklists per card now
              
              fetch(`/admin/crm/cards/${cardId}/checklist/checklist_items/${itemId}`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                  'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                  crm_checklist_item: {
                    content: content
                  }
                })
              })
              .then(response => response.json())
              .then(data => {
                if (data.content) {
                  // Update the UI
                  itemElement.querySelector('.flex-1').textContent = data.content;
                }
              })
              .catch(error => {
                console.error('Error updating checklist item:', error);
                alert('Error updating checklist item. Please try again.');
              });
            }
            
            // Delete a checklist item
            function deleteChecklistItem(itemId, itemElement) {
              const cardId = '<%= @card.id %>';
              // Using multiple checklists per card now
              
              fetch(`/admin/crm/cards/${cardId}/checklist/checklist_items/${itemId}`, {
                method: 'DELETE',
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                  'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                }
              })
              .then(response => {
                if (response.ok) {
                  // Remove the item from the UI
                  itemElement.remove();
                  
                  // Update progress
                  updateChecklistProgress();
                  
                  // If there are no more items, show the 'no items' message
                  const checklistItems = document.getElementById('checklist-items');
                  if (!checklistItems.querySelector('.checklist-item')) {
                    checklistItems.innerHTML = `
                      <div class="text-center text-gray-500 py-4">
                        No checklist items yet. Add one below to get started.
                      </div>
                    `;
                  }
                } else {
                  throw new Error('Failed to delete checklist item');
                }
              })
              .catch(error => {
                console.error('Error deleting checklist item:', error);
                alert('Error deleting checklist item. Please try again.');
              });
            }
            
            // Update checklist title
            function updateChecklistTitle(title) {
              const cardId = '<%= @card.id %>';
              // Using multiple checklists per card now
              
              if (!checklistId) {
                alert('Please create a checklist first');
                return;
              }
              
              fetch(`/admin/crm/cards/${cardId}/checklist`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                  'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                  crm_checklist_item: {
                    title: title
                  }
                })
              })
              .then(response => response.json())
              .then(data => {
                if (data.title) {
                  // Update the UI
                  document.getElementById('checklist-title').textContent = data.title;
                }
              })
              .catch(error => {
                console.error('Error updating checklist title:', error);
                alert('Error updating checklist title. Please try again.');
              });
            }
            
            // Update checklist progress
            function updateChecklistProgress() {
              const checklistItems = document.querySelectorAll('.checklist-item');
              const totalItems = checklistItems.length;
              const completedItems = document.querySelectorAll('.checklist-item .toggle-item.bg-gradient-to-br').length;
              
              // Update progress text
              const progressText = document.getElementById('checklist-progress');
              progressText.textContent = `${completedItems}/${totalItems} completed`;
              
              // Update progress bar
              const progressBar = document.getElementById('progress-bar');
              const percentage = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;
              progressBar.style.width = `${percentage}%`;
            }
            
            // Delete the entire checklist
            function deleteChecklist() {
              const cardId = '<%= @card.id %>';
              // Using multiple checklists per card now
              
              if (!checklistId) {
                alert('No checklist exists to delete');
                return;
              }
              
              fetch(`/admin/crm/cards/${cardId}/checklist`, {
                method: 'DELETE',
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                  'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                }
              })
              .then(response => {
                if (response.ok) {
                  // Reset the checklist UI
                  document.getElementById('checklist-title').textContent = 'Checklist';
                  document.getElementById('checklist-progress').textContent = '0/0 completed';
                  document.getElementById('progress-bar').style.width = '0%';
                  document.getElementById('checklist-items').innerHTML = `
                    <div class="text-center text-gray-500 py-4">
                      No checklist items yet. Add one below to get started.
                    </div>
                  `;
                  
                  // Show a success message
                  alert('Checklist deleted successfully');
                } else {
                  throw new Error('Failed to delete checklist');
                }
              })
              .catch(error => {
                console.error('Error deleting checklist:', error);
                alert('Error deleting checklist. Please try again.');
              });
            }
            
            // Current date for the date picker
            let currentDate = new Date();
            
            // Initialize the date picker
            function initDatePicker() {
              updateCalendar();
            }
            
            // Update the calendar with the current month
            function updateCalendar() {
              const year = currentDate.getFullYear();
              const month = currentDate.getMonth();
              
              // Update month display
              const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
              document.getElementById('currentMonth').textContent = `${monthNames[month]} ${year}`;
              
              // Get first day of month and last day of month
              const firstDay = new Date(year, month, 1);
              const lastDay = new Date(year, month + 1, 0);
              
              // Get day of week for first day (0 = Sunday, 6 = Saturday)
              const firstDayOfWeek = firstDay.getDay();
              
              // Get total days in month
              const totalDays = lastDay.getDate();
              
              // Get days from previous month to display
              const prevMonthLastDay = new Date(year, month, 0).getDate();
              
              // Clear calendar
              const calendarDays = document.getElementById('calendarDays');
              calendarDays.innerHTML = '';
              
              // Add days from previous month
              for (let i = 0; i < firstDayOfWeek; i++) {
                const day = prevMonthLastDay - firstDayOfWeek + i + 1;
                calendarDays.appendChild(createDayButton(day, 'prev-month'));
              }
              
              // Add days from current month
              const today = new Date();
              const isCurrentMonth = today.getMonth() === month && today.getFullYear() === year;
              const todayDate = today.getDate();
              
              for (let i = 1; i <= totalDays; i++) {
                const isToday = isCurrentMonth && i === todayDate;
                calendarDays.appendChild(createDayButton(i, isToday ? 'today' : 'current-month'));
              }
              
              // Add days from next month to fill out the grid
              const totalCells = Math.ceil((firstDayOfWeek + totalDays) / 7) * 7;
              const nextMonthDays = totalCells - (firstDayOfWeek + totalDays);
              
              for (let i = 1; i <= nextMonthDays; i++) {
                calendarDays.appendChild(createDayButton(i, 'next-month'));
              }
            }
            
            // Create a day button for the calendar
            function createDayButton(day, type) {
              const button = document.createElement('button');
              button.type = 'button';
              button.textContent = day;
              button.className = 'aspect-square flex items-center justify-center p-1.5 hover:bg-gray-100 rounded-full';
              
              if (type === 'prev-month' || type === 'next-month') {
                button.classList.add('text-gray-300');
              } else if (type === 'today') {
                button.classList.add('bg-blue-600', 'text-white', 'hover:bg-blue-600', 'font-medium');
              }
              
              // Add click event for date selection
              button.addEventListener('click', function() {
                selectDate(day, type);
              });
              
              return button;
            }
            
            // Handle date selection
            function selectDate(day, type) {
              let selectedDate = new Date(currentDate);
              
              if (type === 'prev-month') {
                selectedDate.setMonth(selectedDate.getMonth() - 1);
              } else if (type === 'next-month') {
                selectedDate.setMonth(selectedDate.getMonth() + 1);
              }
              
              selectedDate.setDate(day);
              
              // Update the last contacted date
              updateLastContactedWithDate(selectedDate);
            }
            
            // Toggle the date picker dropdown
            function toggleDatePicker() {
              const dropdown = document.getElementById('datePickerDropdown');
              dropdown.classList.toggle('hidden');
            }
            
            // Close the date picker
            function closeDatePicker() {
              document.getElementById('datePickerDropdown').classList.add('hidden');
            }
            
            // Go to previous month
            function previousMonth() {
              currentDate.setMonth(currentDate.getMonth() - 1);
              updateCalendar();
            }
            
            // Go to next month
            function nextMonth() {
              currentDate.setMonth(currentDate.getMonth() + 1);
              updateCalendar();
            }
            
            // Select today's date
            function selectToday() {
              currentDate = new Date();
              updateCalendar();
              selectDate(currentDate.getDate(), 'today');
            }
            
            // Toggle the COT category dropdown
            function toggleCotCategoryDropdown() {
              const dropdown = document.getElementById('cotCategoryDropdownMenu');
              dropdown.classList.toggle('hidden');
            }
            
            // Update last contacted with current date (original function)
            function updateLastContacted() {
              fetch(`/admin/crm/cards/<%= @card.id %>/update_last_contacted`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                  'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                }
              })
              .then(response => {
                // Check if the response is JSON
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                  return response.json();
                }
                // If not JSON, handle the redirect manually
                if (response.redirected) {
                  window.location.reload();
                  return { success: true };
                }
                throw new Error('Unexpected response format');
              })
              .then(data => {
                if (data.success) {
                  if (typeof toastr !== 'undefined') {
                    toastr.success('Last contacted time updated');
                  }
                  // Refresh the page to show updated time
                  setTimeout(() => {
                    window.location.reload();
                  }, 1000);
                } else {
                  alert('Error updating last contacted date: ' + (data.errors ? data.errors.join(', ') : 'Unknown error'));
                }
              })
              .catch(error => {
                console.error('Error:', error);
                alert('Network error occurred. Please try again.');
                // Reload the page as a fallback
                window.location.reload();
              });
            }
            
            // Update last contacted with a specific date
            function updateLastContactedWithDate(date) {
              const formattedDate = date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
              
              fetch(`/admin/crm/cards/<%= @card.id %>/update_last_contacted`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json', // Explicitly request JSON response
                  'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ date: formattedDate })
              })
              .then(response => {
                // Check if the response is JSON
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                  return response.json();
                }
                // If not JSON, handle the redirect manually
                if (response.redirected) {
                  window.location.reload();
                  return { success: true };
                }
                throw new Error('Unexpected response format');
              })
              .then(data => {
                if (data.success) {
                  if (typeof toastr !== 'undefined') {
                    toastr.success('Last contacted time updated');
                  }
                  closeDatePicker();
                  // Refresh the page to show updated time
                  setTimeout(() => {
                    window.location.reload();
                  }, 1000);
                } else {
                  alert('Error updating last contacted date: ' + (data.errors ? data.errors.join(', ') : 'Unknown error'));
                  closeDatePicker();
                }
              })
              .catch(error => {
                console.error('Error:', error);
                alert('Network error occurred. Please try again.');
                closeDatePicker();
                // Reload the page as a fallback
                window.location.reload();
              });
            }
          </script>
        </div>
        <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-4 p-4">
          <div class="space-y-4">
            <div>
              <h3 class="text-[14px] font-medium mb-2 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text w-4 h-4 mr-1 text-gray-500">
                  <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                  <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                  <path d="M10 9H8"></path>
                  <path d="M16 13H8"></path>
                  <path d="M16 17H8"></path>
                </svg>
                Description
              </h3>
              <textarea id="card-description" class="tinymce_editor w-full bg-transparent resize-none focus:outline-none text-[14px] text-gray-700 min-h-[106px]" placeholder="Add a more detailed description" data-autosave="true" data-card-id="<%= @card.id %>"><%= @card.description %></textarea>
              
              <!-- Hidden form for description updates -->
              <form id="description-form" action="/admin/crm/cards/<%= @card.id %>/update_description" method="post" style="display: none;">
                <input type="hidden" name="_method" value="patch">
                <input type="hidden" name="authenticity_token" value="<%= form_authenticity_token %>">
                <input type="hidden" name="description" id="description-input">
              </form>
              
              <!-- Description autosave is handled by cards.js -->
            </div>
            
            <!-- Card Labels Section -->
            <div class="mt-4">
              <h3 class="text-[14px] font-medium mb-2 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-tag w-4 h-4 mr-1 text-gray-500">
                  <path d="M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"></path>
                  <circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle>
                </svg>
                Labels
              </h3>
              <div class="card-labels-container flex flex-wrap">
                <% if @card.crm_labels.present? %>
                  <% @card.crm_labels.each do |label| %>
                    <div class="<%= crm_label_text_classes(label) %> text-xs font-medium px-2 py-1 rounded mr-1 mb-1"><%= label.name %></div>
                  <% end %>
                <% else %>
                  <p class="text-sm text-gray-500">No labels added. Use the Labels dropdown in the sidebar to add labels.</p>
                <% end %>
              </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <h3 class="text-[14px] font-medium mb-2 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-list w-4 h-4 mr-1 text-gray-500">
                    <line x1="8" x2="21" y1="6" y2="6"></line>
                    <line x1="8" x2="21" y1="12" y2="12"></line>
                    <line x1="8" x2="21" y1="18" y2="18"></line>
                    <line x1="3" x2="3.01" y1="6" y2="6"></line>
                    <line x1="3" x2="3.01" y1="12" y2="12"></line>
                    <line x1="3" x2="3.01" y1="18" y2="18"></line>
                  </svg>
                  Custom Fields
                </h3>
                <div class="bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200/60 shadow-lg h-full overflow-hidden">
                  <div class="p-5">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-[15px] font-medium flex items-center text-gray-800"><span>Custom Fields</span>
                        <a href="/admin/crm/boards/<%= @board.id %>/custom_fields" class="ml-2 text-gray-400 hover:text-gray-600 transition-colors">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-pen w-3.5 h-3.5">
                            <path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z"></path>
                          </svg>
                        </a>
                      </h4>
                      <a href="/admin/crm/boards/<%= @board.id %>/custom_fields" class="text-gray-500 hover:text-gray-600 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus w-3.5 h-3.5">
                          <path d="M5 12h14"></path>
                          <path d="M12 5v14"></path>
                        </svg>
                      </a>
                    </div>
                    <div class="space-y-3 mb-3" id="custom-fields-container">
                      <% if @custom_fields.any? %>
                        <% @custom_fields.each do |field| %>
                          <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-100 hover:bg-gray-100 transition-colors custom-field-item" data-field-id="<%= field.id %>">
                            <span class="text-[12px] text-gray-600"><%= field.name %></span>
                            <% field_value = @custom_field_values[field.id] %>
                            <% if field.field_type == 'select' %>
                              <select class="custom-field-input text-[12px] font-medium bg-white px-2 py-1 rounded border border-gray-200 focus:border-blue-300 focus:outline-none focus:ring-1 focus:ring-blue-200 text-right min-w-[150px]" data-field-id="<%= field.id %>" data-card-id="<%= @card.id %>">
                                <option value="" <%= field_value.blank? ? 'selected' : '' %>>Select an option</option>
                                <% field.options.each do |option| %>
                                  <option value="<%= option %>" <%= field_value == option ? 'selected' : '' %>><%= option %></option>
                                <% end %>
                              </select>
                            <% elsif field.field_type == 'date' %>
                              <input type="date" class="custom-field-input text-[12px] font-medium bg-white px-2 py-1 rounded border border-gray-200 focus:border-blue-300 focus:outline-none focus:ring-1 focus:ring-blue-200 text-right min-w-[150px]" value="<%= field_value %>" data-field-id="<%= field.id %>" data-card-id="<%= @card.id %>">
                            <% elsif field.field_type == 'number' %>
                              <% if field.has_unit? %>
                                <div class="flex items-center bg-white border border-gray-200 rounded min-w-[150px]">
                                  <% if field.unit_is_prefix? %>
                                    <span class="text-[12px] text-gray-900 px-2 py-1 border-r border-gray-200"><%= field.unit_symbol %></span>
                                  <% end %>
                                  <input type="number" class="custom-field-input text-[12px] font-medium bg-transparent px-2 py-1 focus:border-blue-300 focus:outline-none focus:ring-1 focus:ring-blue-200 text-right flex-1 border-0" value="<%= field_value %>" data-field-id="<%= field.id %>" data-card-id="<%= @card.id %>" step="<%= field.unit_type == 'count' ? '1' : '0.01' %>">
                                  <% if field.unit_is_postfix? %>
                                    <span class="text-[12px] text-gray-900 px-2 py-1 border-l border-gray-200"><%= field.unit_symbol %></span>
                                  <% end %>
                                </div>
                              <% else %>
                                <input type="number" class="custom-field-input text-[12px] font-medium bg-white px-2 py-1 rounded border border-gray-200 focus:border-blue-300 focus:outline-none focus:ring-1 focus:ring-blue-200 text-right min-w-[150px]" value="<%= field_value %>" data-field-id="<%= field.id %>" data-card-id="<%= @card.id %>">
                              <% end %>
                            <% else %>
                              <input type="text" class="custom-field-input text-[12px] font-medium bg-white px-2 py-1 rounded border border-gray-200 focus:border-blue-300 focus:outline-none focus:ring-1 focus:ring-blue-200 text-right min-w-[150px]" value="<%= field_value %>" data-field-id="<%= field.id %>" data-card-id="<%= @card.id %>">
                            <% end %>
                          </div>
                        <% end %>
                      <% else %>
                        <div class="text-center text-gray-500 py-8">
                          <p class="mb-3">No custom fields added to this board yet.</p>
                          <p class="text-sm">Click the + icon to add custom fields.</p>
                        </div>
                      <% end %>
                    </div>
                  </div>
                </div>
                
                
              </div>
              <div>
                <h3 class="text-[14px] font-medium mb-2 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-check-big w-4 h-4 mr-1 text-gray-500">
                    <path d="m9 11 3 3L22 4"></path>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                  </svg>
                  Checklists
                </h3>
                <div class="bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200/60 shadow-lg h-full overflow-hidden">
                  <div class="p-5" data-np-autofill-form-type="other" data-np-checked="1" data-np-watching="1">
                    <div id="checklists-container" class="space-y-4">
                      <% if @card.crm_checklists.any? %>
                        <% @card.crm_checklists.each do |checklist| %>
                          <div class="checklist-container bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden" data-checklist-id="<%= checklist.id %>">
                            <div class="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                              <h3 class="checklist-title text-sm font-semibold text-gray-700"><%= checklist.title %></h3>
                              <div class="flex items-center gap-2">
                                <a href="<%= edit_admin_crm_board_checklist_path(@board.id, checklist) %>" class="text-gray-400 hover:text-gray-600 transition-colors">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pencil w-3.5 h-3.5">
                                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                                    <path d="m15 5 4 4"></path>
                                  </svg>
                                </a>
                                <button class="text-gray-400 hover:text-red-500 transition-colors remove-checklist" data-checklist-id="<%= checklist.id %>">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2 w-3.5 h-3.5">
                                    <path d="M3 6h18"></path>
                                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                    <line x1="10" x2="10" y1="11" y2="17"></line>
                                    <line x1="14" x2="14" y1="11" y2="17"></line>
                                  </svg>
                                </button>
                              </div>
                            </div>
                            
                            <div class="px-4 py-2">
                              <div class="flex items-center text-xs text-gray-500 mb-2">
                                <span class="checklist-progress">
                                  <%= checklist.completed_items_count %>/<%= checklist.items_count %> completed
                                </span>
                                <div class="flex-1 mx-2">
                                  <div class="h-1 bg-gray-100 rounded-full overflow-hidden">
                                    <div class="progress-bar h-full bg-amber-400 rounded-full" style="width: <%= checklist.completion_percentage %>%"></div>
                                  </div>
                                </div>
                              </div>
                              
                              <div class="checklist-items space-y-1 mb-3">
                                <% if checklist.crm_checklist_items.any? %>
                                  <% checklist.crm_checklist_items.order(:position).each do |item| %>
                                    <div class="flex items-center p-2 hover:bg-gray-50 rounded-lg transition-colors group checklist-item" data-item-id="<%= item.id %>">
                                      <div class="w-5 h-5 rounded-md border border-gray-300 flex items-center justify-center mr-3 shadow-sm cursor-pointer checklist-item-checkbox <%= item.completed? ? 'bg-gradient-to-br from-amber-50 to-amber-100 text-amber-700' : '' %>" 
                                           data-item-id="<%= item.id %>" 
                                           data-checklist-id="<%= checklist.id %>" 
                                           data-completed="<%= item.completed? %>">
                                        <% if item.completed? %>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check w-3 h-3">
                                            <path d="M20 6 9 17l-5-5"></path>
                                          </svg>
                                        <% end %>
                                      </div>
                                      <div class="flex-1 text-[12px] text-gray-700 <%= item.completed? ? 'line-through' : '' %>"><%= item.content %></div>
                                    </div>
                                  <% end %>
                                <% else %>
                                  <div class="text-center text-gray-500 py-4">
                                    No checklist items yet.
                                  </div>
                                <% end %>
                              </div>
                            </div>
                          </div>
                        <% end %>
                      <% else %>
                        <div class="text-center text-gray-500 py-8 bg-white rounded-lg shadow-sm border border-gray-200">
                          <p class="mb-3">No checklists added to this card yet.</p>
                          <p class="text-sm">Use the Checklists dropdown in the sidebar to add checklists.</p>
                        </div>
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-10 pt-6 border-t border-gray-200">
              <h3 class="text-[14px] font-medium mb-2 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4 mr-1 text-gray-500">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
                Activity
              </h3>
              
              <!-- Activity Tabs -->
              <div class="mb-4">
                <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm w-fit">
                  <button data-tab="all" onclick="handleActivityTabClick('all', this); return false;" class="tab-button min-w-[36px] pl-3 pr-4 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden text-blue-800" style="background: linear-gradient(to right, #bfdbfe, #93c5fd);">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-history h-4 w-4 text-blue-700">
                      <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                      <path d="M3 3v5h5"></path>
                      <path d="M12 7v5l4 2"></path>
                    </svg>
                    <span class="tab-text text-xs font-medium tracking-wide ml-1.5">All Activity</span>
                  </button>
                  <button data-tab="comments" onclick="handleActivityTabClick('comments', this); return false;" class="tab-button bg-white text-gray-600 hover:bg-gray-50 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-4 w-4">
                      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                    </svg>
                    <span class="tab-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300">Comments</span>
                  </button>
                </div>
              </div>
              <div class="bg-white rounded-lg border border-gray-200 overflow-hidden" data-np-autofill-form-type="other" data-np-checked="1" data-np-watching="1">
                <div class="p-3 border-b border-gray-100">
                  <div class="flex items-start">
                    <div class="w-7 h-7 rounded-full overflow-hidden mr-2">
                      <%= render 'layouts/shared/user_avatar', user: current_user, width: 28, height: 28 %>
                    </div>
                    <div class="flex-1">
                      <input id="new-comment-input" class="w-full px-3 py-1.5 bg-gray-50 rounded-full border border-gray-200 text-[14px] placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Write a comment..." type="text" data-np-intersection-state="visible" onkeydown="if(event.key === 'Enter') { addNewComment(); return false; }">
                    </div>
                  </div>
                </div>
                

                
                <!-- Activity Content -->
                <div id="activity-content">
                  <%= render 'admin/crm/cards/activity_section' %>
                </div>
              </div>
            </div>
            
            <script>
              // Initialize with All Activity tab selected
              document.addEventListener('DOMContentLoaded', function() {
                // All activity items should be visible by default
                const activityItems = document.querySelectorAll('.activity-item, .comment-item');
                activityItems.forEach(item => {
                  item.style.display = 'block';
                });
              });
              
              // Activity tab functionality
              function handleActivityTabClick(tabName, clickedButton) {
                // Update tab styling for all tabs
                const allTabButtons = document.querySelectorAll('.tab-button');
                allTabButtons.forEach(button => {
                  // Reset to default white style
                  button.className = 'tab-button bg-white text-gray-600 hover:bg-gray-50 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden';
                  button.style.background = 'white';
                  
                  // Reset the text span
                  const textSpan = button.querySelector('.tab-text');
                  if (textSpan) {
                    textSpan.classList.add('opacity-0', 'w-0');
                    textSpan.classList.remove('ml-1.5');
                  }
                });
                
                // Set active tab with blue gradient using inline style
                clickedButton.className = 'tab-button text-blue-800 min-w-[36px] pl-3 pr-4 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden';
                clickedButton.style.background = 'linear-gradient(to right, #bfdbfe, #93c5fd)';
                
                // Show text for active tab
                const activeTextSpan = clickedButton.querySelector('.tab-text');
                if (activeTextSpan) {
                  activeTextSpan.classList.remove('opacity-0', 'w-0');
                  activeTextSpan.classList.add('ml-1.5');
                }
                
                // Filter activity items based on selected tab
                const activityItems = document.querySelectorAll('.activity-item, .comment-item');
                
                activityItems.forEach(item => {
                  if (tabName === 'all') {
                    item.style.display = 'block';
                  } else if (tabName === 'comments') {
                    if (item.dataset.activityType === 'comment') {
                      item.style.display = 'block';
                    } else {
                      item.style.display = 'none';
                    }
                  }
                });
              }
              
              // Comment functionality
              function addNewComment() {
                const commentInput = document.getElementById('new-comment-input');
                const content = commentInput.value.trim();
                
                if (!content) return;
                
                const cardId = document.getElementById('card-id').value;
                
                // Show loading indicator
                const submitButton = document.querySelector('.comment-submit-btn');
                if (submitButton) {
                  submitButton.disabled = true;
                  submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...';
                }
                
                fetch(`/admin/crm/cards/${cardId}/comments`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                  },
                  body: JSON.stringify({ content: content })
                })
                .then(response => response.json())
                .then(data => {
                  if (data.success) {
                    // Clear the input
                    commentInput.value = '';
                    
                    // Create and insert the new comment
                    insertNewComment(data.comment);
                    
                    // Show success notification
                    if (window.toastr) {
                      toastr.success('Comment added successfully');
                    }
                  } else {
                    if (window.toastr) {
                      toastr.error('Error adding comment: ' + (data.errors ? data.errors.join(', ') : 'Unknown error'));
                    } else {
                      alert('Error adding comment: ' + (data.errors ? data.errors.join(', ') : 'Unknown error'));
                    }
                  }
                })
                .catch(error => {
                  console.error('Error:', error);
                  if (window.toastr) {
                    toastr.error('An error occurred while adding the comment.');
                  } else {
                    alert('An error occurred while adding the comment.');
                  }
                })
                .finally(() => {
                  // Reset submit button
                  if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.innerHTML = 'Add Comment';
                  }
                });
              }
              
              function insertNewComment(comment) {
                // Get the activity content container
                const activityContent = document.getElementById('activity-content');
                if (!activityContent) {
                  console.error('Activity content container not found');
                  return;
                }
                
                // Find the first div inside activity-content (should be the activities container)
                const activitiesContainer = activityContent.querySelector('.divide-y');
                if (!activitiesContainer) {
                  console.error('Activities container not found');
                  return;
                }
                
                // Get the current user's info
                const currentUserName = document.querySelector('.navbar-user-name')?.textContent.trim() || 'User';
                
                // Create the comment HTML with the correct structure matching the partial
                const commentHtml = `
                  <div class="p-3 comment-item activity-type-comment" data-activity-type="comment" id="comment-${comment.id}">
                    <div class="flex">
                      <div class="w-7 h-7 rounded-full bg-blue-50 flex items-center justify-center mr-2 flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square w-4 h-4 text-blue-500">
                          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                        </svg>
                      </div>
                      <div class="flex-1">
                        <div class="flex justify-between items-start mb-1">
                          <div>
                            <span class="text-[14px] font-medium">${currentUserName}</span>
                          </div>
                          <div class="flex items-center">
                            <span class="text-[12px] text-gray-500 mr-2">just now</span>
                            <div class="relative">
                              <button type="button" onclick="toggleCommentActions(${comment.id})" class="text-gray-400 hover:text-gray-600 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-more-horizontal w-4 h-4">
                                  <circle cx="12" cy="12" r="1"></circle>
                                  <circle cx="19" cy="12" r="1"></circle>
                                  <circle cx="5" cy="12" r="1"></circle>
                                </svg>
                              </button>
                              <div id="comment-actions-${comment.id}" class="absolute right-0 mt-1 w-40 bg-white rounded-md shadow-lg overflow-hidden z-20 border border-gray-200 hidden">
                                <div class="py-1">
                                  <button onclick="showDeleteCommentConfirmation(${comment.id})" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2 w-4 h-4 mr-2 text-gray-500">
                                      <path d="M3 6h18"></path>
                                      <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                      <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                      <line x1="10" x2="10" y1="11" y2="17"></line>
                                      <line x1="14" x2="14" y1="11" y2="17"></line>
                                    </svg>
                                    Delete
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="comment-content mb-2" id="comment-content-${comment.id}">
                          <p class="text-[14px] text-gray-700 bg-gray-50 p-2 rounded-lg">${comment.content}</p>
                        </div>
                        <div class="flex mt-1 space-x-3">
                          <button class="text-[12px] text-gray-500 hover:text-gray-700 flex items-center cursor-pointer" onclick="showEditForm(${comment.id})">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-pen w-3 h-3 mr-1"><path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z"></path></svg>Edit
                          </button>
                          <button class="text-[12px] text-gray-500 hover:text-gray-700 flex items-center cursor-pointer" onclick="deleteComment(${comment.id})">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash w-3 h-3 mr-1"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg>Delete
                          </button>
                          <button class="text-[12px] text-gray-500 hover:text-gray-700 flex items-center cursor-pointer" onclick="showReplyForm(${comment.id})">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-reply w-3 h-3 mr-1"><path d="M3 17v-2a4 4 0 0 1 4-4h10"></path><polyline points="9 7 3 13 9 19"></polyline></svg>Reply
                          </button>
                        </div>
                        <div id="reply-form-container-${comment.id}" class="mt-2 hidden">
                          <form id="reply-form-${comment.id}" class="flex items-start">
                            <div class="flex-1">
                              <textarea class="w-full px-3 py-1.5 bg-gray-50 rounded-lg border border-gray-200 text-[14px] placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Write a reply..." rows="2"></textarea>
                            </div>
                            <div class="ml-2">
                              <button type="button" class="px-3 py-1.5 bg-blue-500 text-white rounded-lg text-[14px] hover:bg-blue-600 transition-colors">
                                Reply
                              </button>
                            </div>
                          </form>
                        </div>
                      </div>
                    </div>
                  </div>
                `;
                
                // Check if there's a "No activity yet" message and remove it if present
                const noActivityMessage = activitiesContainer.querySelector('.text-center.py-4.text-gray-500');
                if (noActivityMessage) {
                  noActivityMessage.remove();
                }
                
                // Insert at the beginning of the activities container
                if (activitiesContainer.firstChild) {
                  activitiesContainer.insertBefore(createElementFromHTML(commentHtml), activitiesContainer.firstChild);
                } else {
                  activitiesContainer.innerHTML = commentHtml;
                }
                
                // Add event listener to the reply button in the new comment
                const commentElement = document.getElementById(`comment-${comment.id}`);
                if (commentElement) {
                  const replyButton = commentElement.querySelector(`#reply-form-${comment.id} button`);
                  if (replyButton) {
                    replyButton.addEventListener('click', function() {
                      submitReply(comment.id);
                    });
                  }
                }
                
                // Update comment count if present
                const commentCount = document.querySelector('.comment-count');
                if (commentCount) {
                  const currentCount = parseInt(commentCount.textContent) || 0;
                  commentCount.textContent = currentCount + 1;
                }
              }
              
              // Helper function to create an element from HTML string
              function createElementFromHTML(htmlString) {
                const div = document.createElement('div');
                div.innerHTML = htmlString.trim();
                return div.firstChild;
              }
              
              function toggleCommentActions(commentId) {
                const actionsMenu = document.getElementById(`comment-actions-${commentId}`);
                if (actionsMenu) {
                  actionsMenu.classList.toggle('hidden');
                }
                
                // Close all other menus
                document.querySelectorAll('.comment-actions-menu').forEach(menu => {
                  if (menu.id !== `comment-actions-${commentId}` && !menu.classList.contains('hidden')) {
                    menu.classList.add('hidden');
                  }
                });
              }
              
              function showReplyForm(commentId) {
                // Hide any open reply forms
                document.querySelectorAll('[id^="reply-form-container-"]').forEach(form => {
                  form.classList.add('hidden');
                });
                
                // Show the reply form for this comment
                const replyForm = document.getElementById(`reply-form-container-${commentId}`);
                if (replyForm) {
                  replyForm.classList.remove('hidden');
                  // Focus the textarea
                  const textarea = replyForm.querySelector('textarea');
                  if (textarea) {
                    textarea.focus();
                  }
                } else {
                  // Create a reply form if it doesn't exist
                  const commentElement = document.getElementById(`comment-${commentId}`);
                  if (!commentElement) return;
                  
                  const commentContent = commentElement.querySelector('.comment-content');
                  if (!commentContent) return;
                  
                  const replyFormHTML = `
                    <div id="reply-form-container-${commentId}" class="mt-2">
                      <form id="reply-form-${commentId}" class="flex items-start">
                        <div class="flex-1">
                          <textarea class="w-full px-3 py-1.5 bg-gray-50 rounded-lg border border-gray-200 text-[14px] placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Write a reply..." rows="2"></textarea>
                        </div>
                        <div class="ml-2">
                          <button type="button" class="px-3 py-1.5 bg-blue-500 text-white rounded-lg text-[14px] hover:bg-blue-600 transition-colors">
                            Reply
                          </button>
                        </div>
                      </form>
                    </div>
                  `;
                  
                  commentContent.insertAdjacentHTML('afterend', replyFormHTML);
                  // Focus the textarea
                  const newTextarea = document.querySelector(`#reply-form-${commentId} textarea`);
                  if (newTextarea) {
                    newTextarea.focus();
                  }
                  
                  // Add event listener to the reply button
                  const replyButton = document.querySelector(`#reply-form-${commentId} button`);
                  if (replyButton) {
                    replyButton.addEventListener('click', function() {
                      submitReply(commentId);
                    });
                  }
                }
              }
              
              function cancelReply(commentId) {
                const replyForm = document.getElementById(`reply-form-${commentId}`);
                if (replyForm) {
                  replyForm.classList.add('hidden');
                  replyForm.querySelector('textarea').value = '';
                }
              }
              
              function insertReply(commentId, reply) {
                // Find the comment element
                const commentElement = document.getElementById(`comment-${commentId}`);
                if (!commentElement) {
                  console.error(`Comment element with ID comment-${commentId} not found`);
                  return;
                }
                
                // Get the current user's info
                const currentUserName = document.querySelector('.navbar-user-name')?.textContent.trim() || 'User';
                
                // Create the reply HTML
                const replyHtml = `
                  <div class="ml-8 mt-2 p-2 bg-gray-50 rounded-md reply-item" id="comment-${reply.id}">
                    <div class="flex justify-between items-start mb-1">
                      <span class="text-[13px] font-medium">${currentUserName}</span>
                      <div class="flex items-center">
                        <span class="text-[12px] text-gray-500 mr-2">just now</span>
                        <div class="relative">
                          <button type="button" onclick="toggleCommentActions(${reply.id})" class="text-gray-400 hover:text-gray-600 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-more-horizontal w-4 h-4">
                              <circle cx="12" cy="12" r="1"></circle>
                              <circle cx="19" cy="12" r="1"></circle>
                              <circle cx="5" cy="12" r="1"></circle>
                            </svg>
                          </button>
                          <div id="comment-actions-${reply.id}" class="absolute right-0 mt-1 w-40 bg-white rounded-md shadow-lg overflow-hidden z-20 border border-gray-200 hidden">
                            <div class="py-1">
                              <button onclick="deleteComment(${reply.id})" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2 w-4 h-4 mr-2 text-gray-500">
                                  <path d="M3 6h18"></path>
                                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                  <line x1="10" x2="10" y1="11" y2="17"></line>
                                  <line x1="14" x2="14" y1="11" y2="17"></line>
                                </svg>
                                Delete
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <p class="text-[13px] text-gray-700">${reply.content}</p>
                  </div>
                `;
                
                // Find where to insert the reply - after existing replies or after the comment content
                const existingReplies = commentElement.querySelectorAll('.reply-item');
                if (existingReplies.length > 0) {
                  // Insert after the last reply
                  const lastReply = existingReplies[existingReplies.length - 1];
                  lastReply.insertAdjacentHTML('afterend', replyHtml);
                } else {
                  // Insert after the comment content
                  const commentContent = commentElement.querySelector('.comment-content');
                  if (commentContent) {
                    commentContent.insertAdjacentHTML('afterend', replyHtml);
                  } else {
                    // Fallback - insert at the end of the comment
                    commentElement.querySelector('.flex-1').insertAdjacentHTML('beforeend', replyHtml);
                  }
                }
                
                // Show success notification
                if (window.toastr) {
                  toastr.success('Reply added successfully');
                }
              }
              
              function submitReply(commentId) {
                const replyForm = document.getElementById(`reply-form-${commentId}`);
                const content = replyForm.querySelector('textarea').value.trim();
                // Fix the button selector to match the button in the form
                const submitButton = replyForm.querySelector('button');
                
                if (!content) return;
                
                // Show loading state
                const originalButtonText = submitButton.innerHTML;
                submitButton.disabled = true;
                submitButton.innerHTML = `
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Submitting...
                `;
                
                const cardId = document.getElementById('card-id').value;
                
                fetch(`/admin/crm/cards/${cardId}/comments`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                  },
                  body: JSON.stringify({ 
                    content: content,
                    parent_id: commentId
                  })
                })
                .then(response => response.json())
                .then(data => {
                  if (data.success) {
                    // Clear and hide the reply form
                    cancelReply(commentId);
                    // Add the reply to the DOM
                    insertReply(commentId, data.comment);
                    // Show success notification
                    if (window.toastr) {
                      toastr.success('Reply added successfully');
                    }
                  } else {
                    // Show error notification
                    if (window.toastr) {
                      toastr.error('Error adding reply: ' + data.errors.join(', '));
                    } else {
                      alert('Error adding reply: ' + data.errors.join(', '));
                    }
                  }
                })
                .catch(error => {
                  console.error('Error:', error);
                  if (window.toastr) {
                    toastr.error('An error occurred while adding the reply');
                  } else {
                    alert('An error occurred while adding the reply');
                  }
                })
                .finally(() => {
                  // Reset button state
                  submitButton.disabled = false;
                  submitButton.innerHTML = originalButtonText;
                });
              }
              
              function showEditForm(commentId) {
                // Hide the actions menu
                const actionsMenu = document.getElementById(`comment-actions-${commentId}`);
                if (actionsMenu) {
                  actionsMenu.classList.add('hidden');
                }
                
                // Hide the comment content
                const commentContent = document.getElementById(`comment-content-${commentId}`);
                if (commentContent) {
                  commentContent.classList.add('hidden');
                }
                
                // Show the edit form
                const editForm = document.getElementById(`edit-form-${commentId}`);
                if (editForm) {
                  editForm.classList.remove('hidden');
                  editForm.querySelector('textarea').focus();
                }
              }
              
              function cancelEdit(commentId) {
                // Show the comment content
                const commentContent = document.getElementById(`comment-content-${commentId}`);
                if (commentContent) {
                  commentContent.classList.remove('hidden');
                }
                
                // Hide the edit form
                const editForm = document.getElementById(`edit-form-${commentId}`);
                if (editForm) {
                  editForm.classList.add('hidden');
                }
              }
              
              function submitEdit(commentId) {
                const editForm = document.getElementById(`edit-form-${commentId}`);
                const content = editForm.querySelector('textarea').value.trim();
                
                if (!content) return;
                
                const cardId = document.getElementById('card-id').value;
                
                fetch(`/admin/crm/cards/${cardId}/comments/${commentId}`, {
                  method: 'PATCH',
                  headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                  },
                  body: JSON.stringify({ content: content })
                })
                .then(response => response.json())
                .then(data => {
                  if (data.success) {
                    // Update the comment content
                    const commentContent = document.getElementById(`comment-content-${commentId}`);
                    if (commentContent) {
                      commentContent.innerHTML = content.replace(/\n/g, '<br>');
                      commentContent.classList.remove('hidden');
                    }
                    
                    // Hide the edit form
                    const editForm = document.getElementById(`edit-form-${commentId}`);
                    if (editForm) {
                      editForm.classList.add('hidden');
                    }
                  } else {
                    alert('Error updating comment: ' + data.errors.join(', '));
                  }
                })
                .catch(error => {
                  console.error('Error:', error);
                  alert('An error occurred while updating the comment.');
                });
              }
              
              function deleteComment(commentId) {
  window.modalUtils.showModal({
    title: 'Delete Comment',
    content: `<div class="text-center text-red-600 font-semibold mb-2">Are you sure you want to delete this comment?</div>
      <div class="text-center text-gray-600 text-sm mb-4">This action cannot be undone.</div>`,
    showCancelButton: true,
    showConfirmButton: true,
    confirmButtonText: 'Delete',
    cancelButtonText: 'Cancel',
    confirmButtonColor: 'bg-red-500 hover:bg-red-600 text-white',
    cancelButtonColor: 'bg-gray-100 hover:bg-gray-200 text-gray-700',
    focusConfirm: false,
    width: 'max-w-md',
    onConfirm: () => {
      const cardId = document.getElementById('card-id').value;
      fetch(`/admin/crm/cards/${cardId}/comments/${commentId}`, {
        method: 'DELETE',
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const commentElement = document.getElementById(`comment-${commentId}`);
          if (commentElement) {
            commentElement.remove();
          }
          toastr.success('Comment deleted', 'Success');
        } else {
          toastr.error('Error deleting comment: ' + (data.errors ? data.errors.join(', ') : 'Unknown error'), 'Error');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        toastr.error('An error occurred while deleting the comment.', 'Error');
      });
    },
    onCancel: null
  });
}


              
              // Close comment action menus when clicking outside
              document.addEventListener('click', function(e) {
                if (!e.target.closest('.comment-actions-button') && !e.target.closest('.comment-actions-menu')) {
                  document.querySelectorAll('.comment-actions-menu').forEach(menu => {
                    if (!menu.classList.contains('hidden')) {
                      menu.classList.add('hidden');
                    }
                  });
                }
              });
            </script>
          </div>
        </div>
      </div>
      <%= render 'admin/crm/cards/sidebar_action_items' %>
    </div>
  </main>
  
  <!-- Include the move card modal -->
  <%= render 'admin/crm/cards/move_card_modal' %>
  
  <!-- Include the archive card modal -->
  <%= render 'admin/crm/cards/archive_card_modal' %>

  <!-- CRM Assigned Team Modal -->
  <% if @card.patient.present? %>
    <%= render 'admin/crm/cards/assigned_team_modal', patient: @card.patient %>
  <% end %>
</div>

<%= render 'admin/calendar_bookings/offcanvas', id: 'slot-finder-offcanvas', title: 'Slot Finder' %>






