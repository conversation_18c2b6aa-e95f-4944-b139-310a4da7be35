# frozen_string_literal: true

module Admin
  module <PERSON><PERSON><PERSON><PERSON>
    def self.time_range_by_param(range_param, time_zone = Time.zone.name)
      now = Time.now.in_time_zone(time_zone)
      case range_param
      when 'today'
        [now.at_beginning_of_day, now.at_end_of_day]
      when 'last_week'
        [(now - 1.week).at_beginning_of_week, (now - 1.week).at_end_of_week]
      when 'this_week'
        [now.at_beginning_of_week, now.at_end_of_week]
      when 'this_month'
        [now.at_beginning_of_month, now.at_end_of_month]
      when 'last_month'
        [(now - 1.month).at_beginning_of_month, (now - 1.month).at_end_of_month]
      when 'last_3_months'
        [(now - 3.months).at_beginning_of_month, (now - 1.month).at_end_of_month]
      when 'last_6_months'
        [(now - 6.months).at_beginning_of_month, (now - 5.months).at_end_of_month]
      when 'last_12_months'
        [(now - 12.months).at_beginning_of_month, (now - 11.months).at_end_of_month]
      when 'last_24_months'
        [(now - 24.months).at_beginning_of_month, (now - 23.months).at_end_of_month]
      when 'last_36_months'
        [(now - 36.months).at_beginning_of_month, (now - 35.months).at_end_of_month]
      end
    end

    # Returns a precise time ago in words without 'about' prefix
    def precise_time_ago_in_words(from_time, include_seconds: false)
      return '' if from_time.nil?

      distance_in_seconds = (Time.current - from_time.in_time_zone).round
      distance_in_minutes = (distance_in_seconds / 60.0).round

      case distance_in_minutes
      when 0..1
        return distance_in_minutes.zero? ? 'less than 1 minute' : '1 minute' unless include_seconds

        case distance_in_seconds
        when 0..4   then 'less than 5 seconds'
        when 5..9   then 'less than 10 seconds'
        when 10..19 then 'less than 20 seconds'
        when 20..39 then 'half a minute'
        when 40..59 then 'less than 1 minute'
        else             '1 minute'
        end
      when 2..44           then "#{distance_in_minutes} minutes"
      when 45..89          then '1 hour'
      when 90..1439        then "#{(distance_in_minutes.to_f / 60.0).round} hours"
      when 1440..2519      then '1 day'
      when 2520..43_199 then "#{(distance_in_minutes.to_f / 1440.0).round} days"
      when 43_200..86_399    then '1 month'
      when 86_400..525_599   then "#{(distance_in_minutes.to_f / 43_200.0).round} months"
      when 525_600..1_051_199 then '1 year'
      else "#{(distance_in_minutes.to_f / 525_600.0).round} years"
      end
    end
  end
end
